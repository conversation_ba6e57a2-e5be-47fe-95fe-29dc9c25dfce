"use client";

import { useCallback, useEffect, useState } from "react";
import { useE<PERSON><PERSON>, EditorContent, BubbleMenu } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import Placeholder from "@tiptap/extension-placeholder";
import HorizontalRule from "@tiptap/extension-horizontal-rule";
import BulletList from "@tiptap/extension-bullet-list";
import OrderedList from "@tiptap/extension-ordered-list";
import ListItem from "@tiptap/extension-list-item";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import FontFamily from "@tiptap/extension-font-family";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import { ResizableImage } from "./extensions/ResizableImageExtension";
import { EnhancedImageUploader } from "./EnhancedImageUploader";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import logger from "@/lib/secureLogger";
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link as LinkIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Heading1,
  Heading2,
  Heading3,
  Code,
  Quote,
  Undo,
  Redo,
  Minus,
  CheckSquare,
  Palette,
  Highlighter,
  Eraser,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { EditorToolbar } from "./EditorToolbar";

interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  editable?: boolean;
  onEditorReady?: (editorData: {
    editor: any;
    handleImageUpload: () => void;
    handleLinkDialog: () => void;
    insertTable: () => void;
  }) => void;
}

// Font families for the editor
const fontFamilies = [
  { name: "Sans-serif", value: "Arial, Helvetica, sans-serif" },
  { name: "Serif", value: "Georgia, Times New Roman, serif" },
  { name: "Monospace", value: "Consolas, Monaco, monospace" },
  { name: "Cursive", value: "Brush Script MT, cursive" },
];

// Image size options
const imageSizeOptions = [
  { name: "ES", width: 150 },
  { name: "S", width: 300 },
  { name: "M", width: 500 },
  { name: "L", width: 800 },
  { name: "XL", width: 1024 },
  { name: "XXL", width: 1280 },
];

export function TipTapEditor({
  content,
  onChange,
  placeholder = "Write your content here...",
  editable = true,
  onEditorReady,
}: TipTapEditorProps) {
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");
  const [selectedText, setSelectedText] = useState("");
  const [wordCount, setWordCount] = useState({ words: 0, characters: 0 });

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configure heading to preserve marks like links
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
        // Don't disable these list extensions since we're providing our own
        // bulletList: false,
        // orderedList: false,
        // listItem: false,
      }),
      Underline,
      // Text styling extensions
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      // List extensions
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6',
        },
      }),
      ListItem.configure({
        HTMLAttributes: {
          class: 'my-2',
        },
      }),
      // Task list extensions
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'task-item',
        },
        nested: true,
      }),
      // Image extensions
      ResizableImage.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
        allowBase64: true,
        inline: true,
      }),
      // Other extensions
      Link.configure({
        openOnClick: true, // Enable direct click to open links
        HTMLAttributes: {
          class: "editor-link",
          target: "_blank",
          rel: "noopener noreferrer",
          style: "color: #2563eb; text-decoration: underline; cursor: pointer;",
        },
        validate: href => /^https?:\/\//.test(href),
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      TextAlign.configure({
        types: ["heading", "paragraph", "image", "resizableImage"],
      }),
      HorizontalRule,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());

      // Auto-save to session storage
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.setItem('blog-editor-content', editor.getHTML());
          logger.info('Content auto-saved to session storage');
        } catch (error) {
          logger.error('Failed to auto-save content');
        }
      }

      // Update word count
      const text = editor.getText();
      const words = text.split(/\s+/).filter(word => word.length > 0).length;
      const characters = text.length;
      setWordCount({ words, characters });
    },
    editorProps: {
      attributes: {
        class: "prose prose-lg dark:prose-invert focus:outline-none max-w-none min-h-[500px] p-4",
      },
    },
  });

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  // Call onEditorReady when editor is initialized and functions are ready
  useEffect(() => {
    if (editor && onEditorReady) {
      // Create the functions inline to avoid hoisting issues
      const imageUploadHandler = () => setIsImageDialogOpen(true);
      const linkDialogHandler = () => {
        if (editor) {
          const selection = editor.state.selection;
          const selectedText = selection.empty
            ? ""
            : editor.state.doc.textBetween(selection.from, selection.to);

          setSelectedText(selectedText);
          setLinkText(selectedText);

          const { from, to } = selection;
          const linkMark = editor.state.doc.rangeHasMark(from, to, editor.schema.marks.link);

          if (linkMark) {
            const linkAttrs = editor.getAttributes('link');
            setLinkUrl(linkAttrs.href || "");
          } else {
            setLinkUrl("");
          }

          setIsLinkDialogOpen(true);
        }
      };
      const tableInsertHandler = () => {
        if (editor) {
          editor
            .chain()
            .focus()
            .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
            .run();
        }
      };

      onEditorReady({
        editor,
        handleImageUpload: imageUploadHandler,
        handleLinkDialog: linkDialogHandler,
        insertTable: tableInsertHandler,
      });
    }
  }, [editor, onEditorReady]);

  // Check for auto-saved content on mount
  useEffect(() => {
    if (editor && typeof window !== 'undefined') {
      try {
        const savedContent = sessionStorage.getItem('blog-editor-content');
        if (savedContent && savedContent !== '<p></p>' && savedContent !== editor.getHTML()) {
          // Ask user if they want to restore
          const restore = confirm('We found auto-saved content. Would you like to restore it?');
          if (restore) {
            editor.commands.setContent(savedContent);
            onChange(savedContent);
            toast({
              title: 'Content restored',
              description: 'Your auto-saved content has been restored.',
            });
          } else {
            // Clear the auto-saved content
            sessionStorage.removeItem('blog-editor-content');
          }
        }
      } catch (error) {
        logger.error('Failed to restore auto-saved content');
      }
    }
  }, [editor, onChange]);

  // Clear formatting
  const clearFormatting = useCallback(() => {
    if (editor) {
      editor.chain().focus().unsetAllMarks().clearNodes().run();
    }
  }, [editor]);

  // Handle image upload
  const handleImageUpload = useCallback(() => {
    setIsImageDialogOpen(true);
  }, []);

  // Insert image
  const insertImage = useCallback((imageData: {
    url: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
    alignment?: 'left' | 'center' | 'right';
    sizeOption?: string;
  }) => {
    if (editor) {
      // Get size based on selected option
      let width = imageData.width;
      let height = imageData.height;

      if (imageData.sizeOption && imageData.sizeOption !== 'custom') {
        const sizeOption = imageSizeOptions.find(option => option.name === imageData.sizeOption);
        if (sizeOption) {
          // Calculate height proportionally if we have original dimensions
          if (imageData.width && imageData.height) {
            const aspectRatio = imageData.height / imageData.width;
            width = sizeOption.width;
            height = Math.round(width * aspectRatio);
          } else {
            width = sizeOption.width;
          }
        }
      }

      // Convert sizeOption to the correct type
      const sizeOptionValue = (imageData.sizeOption || 'custom') as 'ES' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'custom';

      // Create image attributes
      const attrs = {
        src: imageData.url,
        alt: imageData.alt || '',
        title: imageData.title || '',
        width: width,
        height: height,
        alignment: imageData.alignment || 'center',
        sizeOption: sizeOptionValue,
      };

      // Use the resizable image extension
      editor.chain().focus().setResizableImage(attrs).run();

      // Close the dialog
      setIsImageDialogOpen(false);

      // Show success message
      toast({
        title: 'Image inserted',
        description: 'The image has been inserted into your content. Click on it to resize or align.',
      });
    }
  }, [editor, imageSizeOptions]);

  // Handle link insertion
  const handleLinkDialog = useCallback(() => {
    if (editor) {
      const selection = editor.state.selection;
      const selectedText = selection.empty
        ? ""
        : editor.state.doc.textBetween(selection.from, selection.to);

      setSelectedText(selectedText);
      setLinkText(selectedText);

      // Check if cursor is inside a link or if selection contains a link
      const linkAttrs = editor.getAttributes('link');
      if (linkAttrs.href) {
        // If we're editing an existing link, populate the URL field
        setLinkUrl(linkAttrs.href);
      } else {
        setLinkUrl("");
      }

      setIsLinkDialogOpen(true);
    }
  }, [editor]);

  // Insert link
  const insertLink = useCallback(() => {
    if (editor) {
      // If there's no selection and we have link text, insert it
      if (selectedText === "" && linkText) {
        editor
          .chain()
          .focus()
          .insertContent(linkText)
          .setTextSelection({
            from: editor.state.selection.from - linkText.length,
            to: editor.state.selection.from
          })
          .setLink({ href: linkUrl })
          .run();
      } else {
        // Otherwise just apply the link to the selection
        editor
          .chain()
          .focus()
          .setLink({ href: linkUrl })
          .run();
      }

      setIsLinkDialogOpen(false);
    }
  }, [editor, linkUrl, linkText, selectedText]);

  // Handle table insertion
  const insertTable = useCallback(() => {
    if (editor) {
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run();
    }
  }, [editor]);

  // Handle horizontal rule insertion
  const insertHorizontalRule = useCallback(() => {
    if (editor) {
      editor.chain().focus().setHorizontalRule().run();
    }
  }, [editor]);

  // Memoized callbacks for dialog open state changes
  const handleImageDialogOpenChange = useCallback((open: boolean) => {
    setIsImageDialogOpen(open);
  }, []);

  const handleLinkDialogOpenChange = useCallback((open: boolean) => {
    setIsLinkDialogOpen(open);
  }, []);

  if (!editor) {
    return <div className="h-64 w-full bg-muted animate-pulse rounded-md" />;
  }

  return (
    <div className="tiptap-editor border border-input rounded-md bg-background">
      <style jsx global>{`
        .task-list {
          list-style-type: none;
          padding-left: 0;
        }
        .task-list li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.5em;
        }
        .task-list li > label {
          margin-right: 0.5em;
          user-select: none;
        }
        .task-list li > div {
          flex: 1;
        }
        .task-list input[type="checkbox"] {
          margin-top: 0.2em;
        }

        /* Ensure links work properly in all contexts including headings */
        .ProseMirror a,
        .ProseMirror a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
          border: none !important;
          background: none !important;
        }
        .ProseMirror a:hover,
        .ProseMirror a[href]:hover {
          color: #1d4ed8 !important;
          text-decoration: underline !important;
        }

        /* Ensure links in headings maintain link styling */
        .ProseMirror h1 a,
        .ProseMirror h1 a[href],
        .ProseMirror h2 a,
        .ProseMirror h2 a[href],
        .ProseMirror h3 a,
        .ProseMirror h3 a[href],
        .ProseMirror h4 a,
        .ProseMirror h4 a[href],
        .ProseMirror h5 a,
        .ProseMirror h5 a[href],
        .ProseMirror h6 a,
        .ProseMirror h6 a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
          border: none !important;
          background: none !important;
        }
        .ProseMirror h1 a:hover,
        .ProseMirror h1 a[href]:hover,
        .ProseMirror h2 a:hover,
        .ProseMirror h2 a[href]:hover,
        .ProseMirror h3 a:hover,
        .ProseMirror h3 a[href]:hover,
        .ProseMirror h4 a:hover,
        .ProseMirror h4 a[href]:hover,
        .ProseMirror h5 a:hover,
        .ProseMirror h5 a[href]:hover,
        .ProseMirror h6 a:hover,
        .ProseMirror h6 a[href]:hover {
          color: #1d4ed8 !important;
          text-decoration: underline !important;
        }

        /* Override any prose styles that might interfere */
        .prose a,
        .prose a[href],
        .prose-lg a,
        .prose-lg a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
        }
        .prose a:hover,
        .prose a[href]:hover,
        .prose-lg a:hover,
        .prose-lg a[href]:hover {
          color: #1d4ed8 !important;
        }

        /* Dark mode link styles */
        .dark .ProseMirror a,
        .dark .ProseMirror a[href] {
          color: #60a5fa !important;
        }
        .dark .ProseMirror a:hover,
        .dark .ProseMirror a[href]:hover {
          color: #93c5fd !important;
        }

        /* Editor link class specific styling */
        .editor-link {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
        }
        .editor-link:hover {
          color: #1d4ed8 !important;
        }

        /* Force link styling on all anchor tags in editor */
        .ProseMirror-focused a,
        .ProseMirror-focused a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
        }
      `}</style>

      {/* Editor Toolbar */}
      <EditorToolbar
        editor={editor}
        onLinkClick={handleLinkDialog}
        onInsertTable={insertTable}
      />

      {/* Editor Content */}
      <div className="p-4">
        <EditorContent editor={editor} />
      </div>

      {/* Word Count - Fixed at the bottom */}
      <div className="flex justify-between items-center p-3 text-xs text-muted-foreground border-t border-input bg-card sticky bottom-0 z-10">
        <div>
          {wordCount.words} words | {wordCount.characters} characters
        </div>
      </div>

      {/* Image Upload Dialog */}
      <Dialog
        open={isImageDialogOpen}
        onOpenChange={handleImageDialogOpenChange}
      >
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <EnhancedImageUploader
              label="Upload or select an image"
              onImageUpload={insertImage}
              maxFileSizeMB={50}
              maxFiles={1}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Link Dialog */}
      <Dialog
        open={isLinkDialogOpen}
        onOpenChange={handleLinkDialogOpenChange}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {selectedText === "" && (
              <div className="space-y-2">
                <Label htmlFor="link-text">Link Text</Label>
                <Input
                  id="link-text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  placeholder="Enter link text"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="link-url">URL</Label>
              <Input
                id="link-url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
              />
            </div>
            <div className="flex justify-between items-center">
              {/* Remove Link Button - only show if editing existing link */}
              {editor?.getAttributes('link').href && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => {
                    editor.chain().focus().unsetLink().run();
                    setIsLinkDialogOpen(false);
                  }}
                >
                  Remove Link
                </Button>
              )}

              <div className="flex gap-2 ml-auto">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsLinkDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={insertLink}
                  disabled={!linkUrl}
                >
                  {editor?.getAttributes('link').href ? 'Update Link' : 'Insert Link'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bubble Menu */}
      {editor && (
        <BubbleMenu
          editor={editor}
          tippyOptions={{ duration: 100 }}
          className="bg-background border border-input rounded-md shadow-md p-1 flex items-center gap-1"
        >
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("bold") ? "default" : "ghost"}
            onClick={() => editor.chain().focus().toggleBold().run()}
            className="h-7 w-7"
          >
            <Bold className="h-3.5 w-3.5" />
          </Button>
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("italic") ? "default" : "ghost"}
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className="h-7 w-7"
          >
            <Italic className="h-3.5 w-3.5" />
          </Button>
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("link") ? "default" : "ghost"}
            onClick={handleLinkDialog}
            className="h-7 w-7"
          >
            <LinkIcon className="h-3.5 w-3.5" />
          </Button>
        </BubbleMenu>
      )}
    </div>
  );
}
