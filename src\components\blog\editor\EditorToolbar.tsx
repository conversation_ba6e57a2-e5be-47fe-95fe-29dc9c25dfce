"use client";

import { Editor } from '@tiptap/react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  CheckSquare,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link as LinkIcon,
  Table as TableIcon,
  Undo,
  Redo,
  Palette,
  Type,
  ChevronDown,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ToolbarImageUploader } from "./ToolbarImageUploader";

interface EditorToolbarProps {
  editor: Editor;
  onLinkClick: () => void;
  onInsertTable: () => void;
}

// Font families for the editor
const fontFamilies = [
  { name: "Default", value: "inherit" },
  { name: "<PERSON><PERSON>", value: "Arial, sans-serif" },
  { name: "Georgia", value: "Georgia, serif" },
  { name: "<PERSON><PERSON><PERSON>", value: "Helvetica, sans-serif" },
  { name: "<PERSON>", value: "Times New Roman, serif" },
  { name: "Courier", value: "Courier New, monospace" },
  { name: "Verdana", value: "Verdana, sans-serif" },
];

// Heading options
const headingOptions = [
  { name: "Normal", value: "paragraph", level: 0 },
  { name: "Heading 1", value: "heading", level: 1 },
  { name: "Heading 2", value: "heading", level: 2 },
  { name: "Heading 3", value: "heading", level: 3 },
  { name: "Heading 4", value: "heading", level: 4 },
  { name: "Heading 5", value: "heading", level: 5 },
  { name: "Heading 6", value: "heading", level: 6 },
];

// Color palette
const colors = [
  "#000000", "#ffffff", "#ff0000", "#00ff00", "#0000ff", "#ffff00",
  "#ff00ff", "#00ffff", "#808080", "#800000", "#808000", "#008000",
  "#800080", "#008080", "#000080", "#c0c0c0", "#ff8000", "#8000ff"
];



export function EditorToolbar({
  editor,
  onLinkClick,
  onInsertTable,
}: EditorToolbarProps) {
  if (!editor) {
    return null;
  }

  return (
    <div className="flex items-center gap-1 p-2 border-b border-input bg-background shadow-sm overflow-x-auto">
      {/* Text Formatting Group */}
      <div className="flex items-center gap-1">
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("bold") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleBold().run()}
          className="h-7 w-7"
          title="Bold"
        >
          <Bold className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("italic") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className="h-7 w-7"
          title="Italic"
        >
          <Italic className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("underline") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className="h-7 w-7"
          title="Underline"
        >
          <UnderlineIcon className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("strike") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className="h-7 w-7"
          title="Strikethrough"
        >
          <Strikethrough className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      {/* Alignment Group */}
      <div className="flex items-center gap-1">
        <Button
          type="button"
          size="icon"
          variant={editor.isActive({ textAlign: "left" }) ? "default" : "ghost"}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
          className="h-7 w-7"
          title="Align Left"
        >
          <AlignLeft className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive({ textAlign: "center" }) ? "default" : "ghost"}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
          className="h-7 w-7"
          title="Align Center"
        >
          <AlignCenter className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive({ textAlign: "right" }) ? "default" : "ghost"}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
          className="h-7 w-7"
          title="Align Right"
        >
          <AlignRight className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive({ textAlign: "justify" }) ? "default" : "ghost"}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
          className="h-7 w-7"
          title="Justify"
        >
          <AlignJustify className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      {/* Font Family Dropdown */}
      <Select
        value={editor.getAttributes('textStyle')?.fontFamily || "inherit"}
        onValueChange={(value: string) => {
          if (value === "inherit") {
            editor.chain().focus().unsetFontFamily().run();
          } else {
            editor.chain().focus().setFontFamily(value).run();
          }
        }}
      >
        <SelectTrigger className="h-7 w-[120px] text-xs">
          <Type className="h-3 w-3 mr-1" />
          <SelectValue placeholder="Font" />
        </SelectTrigger>
        <SelectContent>
          {fontFamilies.map((font) => (
            <SelectItem key={font.value} value={font.value}>
              {font.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Headings Dropdown */}
      <Select
        value={
          editor.isActive("heading", { level: 1 }) ? "heading-1" :
          editor.isActive("heading", { level: 2 }) ? "heading-2" :
          editor.isActive("heading", { level: 3 }) ? "heading-3" :
          editor.isActive("heading", { level: 4 }) ? "heading-4" :
          editor.isActive("heading", { level: 5 }) ? "heading-5" :
          editor.isActive("heading", { level: 6 }) ? "heading-6" :
          "paragraph"
        }
        onValueChange={(value: string) => {
          if (value === "paragraph") {
            editor.chain().focus().setParagraph().run();
          } else {
            const level = parseInt(value.split("-")[1]);
            editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run();
          }
        }}
      >
        <SelectTrigger className="h-7 w-[100px] text-xs">
          <SelectValue placeholder="Style" />
          <ChevronDown className="h-3 w-3 ml-1" />
        </SelectTrigger>
        <SelectContent>
          {headingOptions.map((heading) => (
            <SelectItem
              key={heading.level === 0 ? "paragraph" : `heading-${heading.level}`}
              value={heading.level === 0 ? "paragraph" : `heading-${heading.level}`}
            >
              {heading.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Color Picker */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7"
            title="Text Color"
          >
            <Palette className="h-3.5 w-3.5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-2">
          <div className="grid grid-cols-6 gap-1">
            {colors.map((color) => (
              <button
                key={color}
                className="w-6 h-6 rounded-md border border-input hover:scale-110 transition-transform"
                style={{ backgroundColor: color }}
                onClick={() => editor.chain().focus().setColor(color).run()}
                title={color}
              />
            ))}
          </div>
          <div className="mt-2 pt-2 border-t border-input">
            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={() => editor.chain().focus().unsetColor().run()}
              className="w-full text-xs"
            >
              Remove Color
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      <div className="w-px h-6 bg-border mx-1" />

      {/* Lists Group */}
      <div className="flex items-center gap-1">
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("bulletList") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className="h-7 w-7"
          title="Bullet List"
        >
          <List className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("orderedList") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className="h-7 w-7"
          title="Numbered List"
        >
          <ListOrdered className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("taskList") ? "default" : "ghost"}
          onClick={() => editor.chain().focus().toggleTaskList().run()}
          className="h-7 w-7"
          title="Task List"
        >
          <CheckSquare className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      {/* Insert Elements Group */}
      <div className="flex items-center gap-1">
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("link") ? "default" : "ghost"}
          onClick={onLinkClick}
          className="h-7 w-7"
          title="Insert Link"
        >
          <LinkIcon className="h-3.5 w-3.5" />
        </Button>
        <ToolbarImageUploader
          onImageInsert={(imageData) => {
            editor.chain().focus().setImage({
              src: imageData.url,
              alt: imageData.alt || '',
              title: imageData.title || '',
            }).run();
          }}
        />
        <Button
          type="button"
          size="icon"
          variant={editor.isActive("table") ? "default" : "ghost"}
          onClick={onInsertTable}
          className="h-7 w-7"
          title="Insert Table"
        >
          <TableIcon className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      {/* Actions Group */}
      <div className="flex items-center gap-1">
        <Button
          type="button"
          size="icon"
          variant="ghost"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          className="h-7 w-7"
          title="Undo"
        >
          <Undo className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          size="icon"
          variant="ghost"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          className="h-7 w-7"
          title="Redo"
        >
          <Redo className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
}
