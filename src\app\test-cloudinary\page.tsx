'use client';

import { useState, useEffect } from 'react';
import { CloudinaryImageUploader } from '@/components/admin/CloudinaryImageUploader';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function TestCloudinaryPage() {
  const [cloudinaryStatus, setCloudinaryStatus] = useState<{
    configured: boolean;
    cloudName: string | null;
  } | null>(null);
  const [uploadedImage, setUploadedImage] = useState<{
    url: string;
    publicId: string;
    width: number;
    height: number;
    alt?: string;
    credit?: string;
  } | null>(null);

  useEffect(() => {
    // Check Cloudinary status
    fetch('/api/upload/cloudinary')
      .then(res => res.json())
      .then(data => setCloudinaryStatus(data))
      .catch(err => console.error('Failed to check Cloudinary status:', err));
  }, []);

  const handleImageUpload = (result: {
    url: string;
    publicId: string;
    width: number;
    height: number;
    alt?: string;
    credit?: string;
  }) => {
    setUploadedImage(result);
    console.log('Image uploaded:', result);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Cloudinary Integration Test</h1>
        <p className="text-muted-foreground">
          Test the Cloudinary image upload functionality
        </p>
      </div>

      {/* Cloudinary Status */}
      <Card>
        <CardHeader>
          <CardTitle>Cloudinary Configuration Status</CardTitle>
        </CardHeader>
        <CardContent>
          {cloudinaryStatus ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span>Status:</span>
                <Badge variant={cloudinaryStatus.configured ? 'default' : 'destructive'}>
                  {cloudinaryStatus.configured ? 'Configured' : 'Not Configured'}
                </Badge>
              </div>
              {cloudinaryStatus.cloudName && (
                <div className="flex items-center gap-2">
                  <span>Cloud Name:</span>
                  <code className="bg-muted px-2 py-1 rounded text-sm">
                    {cloudinaryStatus.cloudName}
                  </code>
                </div>
              )}
            </div>
          ) : (
            <div>Loading status...</div>
          )}
        </CardContent>
      </Card>

      {/* Image Upload Test */}
      <Card>
        <CardHeader>
          <CardTitle>Image Upload Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <CloudinaryImageUploader
            label="Test Image Upload"
            onImageUpload={handleImageUpload}
            type="blog-content"
            showMetadata={true}
          />

          {uploadedImage && (
            <div className="mt-6 p-4 border rounded-lg bg-muted/50">
              <h3 className="font-semibold mb-2">Upload Result:</h3>
              <div className="space-y-2 text-sm">
                <div><strong>URL:</strong> <a href={uploadedImage.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{uploadedImage.url}</a></div>
                <div><strong>Public ID:</strong> <code>{uploadedImage.publicId}</code></div>
                <div><strong>Dimensions:</strong> {uploadedImage.width} × {uploadedImage.height}</div>
                {uploadedImage.alt && <div><strong>Alt Text:</strong> {uploadedImage.alt}</div>}
                {uploadedImage.credit && <div><strong>Credit:</strong> {uploadedImage.credit}</div>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Different Upload Types */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Blog Cover</CardTitle>
          </CardHeader>
          <CardContent>
            <CloudinaryImageUploader
              label="Cover Image"
              onImageUpload={(result) => console.log('Cover uploaded:', result)}
              type="blog-cover"
              showMetadata={false}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Blog Featured</CardTitle>
          </CardHeader>
          <CardContent>
            <CloudinaryImageUploader
              label="Featured Image"
              onImageUpload={(result) => console.log('Featured uploaded:', result)}
              type="blog-featured"
              showMetadata={false}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Blog Content</CardTitle>
          </CardHeader>
          <CardContent>
            <CloudinaryImageUploader
              label="Content Image"
              onImageUpload={(result) => console.log('Content uploaded:', result)}
              type="blog-content"
              showMetadata={false}
            />
          </CardContent>
        </Card>
      </div>

      <div className="text-center">
        <Button asChild>
          <a href="/admin/blog/posts">Go to Blog Admin</a>
        </Button>
      </div>
    </div>
  );
}
