import { NextRequest, NextResponse } from "next/server";
import { uploadToCloudinary, uploadFromUrl, validateCloudinaryConfig } from "@/lib/cloudinary";
import { z } from "zod";

// Validation schemas
const FileUploadSchema = z.object({
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

const UrlUploadSchema = z.object({
  imageUrl: z.string().url("Must be a valid URL"),
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

// Define allowed file types
const allowedFileTypes = [
  "image/jpeg",
  "image/jpg", 
  "image/png",
  "image/gif",
  "image/webp",
  "image/svg+xml",
];

// Define maximum file size (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can upload files
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Validate Cloudinary configuration
    if (!validateCloudinaryConfig()) {
      return NextResponse.json(
        { error: "Cloudinary configuration is missing" },
        { status: 500 }
      );
    }

    const contentType = request.headers.get("content-type");

    // Handle file upload
    if (contentType?.includes("multipart/form-data")) {
      return await handleFileUpload(request);
    }

    // Handle URL upload
    if (contentType?.includes("application/json")) {
      return await handleUrlUpload(request);
    }

    return NextResponse.json(
      { error: "Unsupported content type" },
      { status: 400 }
    );

  } catch (error) {
    console.error("POST /api/upload/cloudinary error:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

async function handleFileUpload(request: NextRequest) {
  try {
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string || "blog-content";
    const folder = formData.get("folder") as string;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate input
    const validation = FileUploadSchema.safeParse({ type, folder });
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      return NextResponse.json(
        { error: `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}` },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size exceeds the limit of 50MB" },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);

    // Upload to Cloudinary with optimizations
    const result = await uploadToCloudinary(buffer, {
      folder: uploadFolder,
      quality: "auto",
      format: "auto",
      transformation: [
        { quality: "auto" },
        { fetch_format: "auto" }
      ]
    });

    return NextResponse.json({
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      type: type,
    });

  } catch (error) {
    console.error("File upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload file to Cloudinary" },
      { status: 500 }
    );
  }
}

async function handleUrlUpload(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validation = UrlUploadSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    const { imageUrl, type, folder } = validation.data;

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);

    // Upload from URL to Cloudinary
    const result = await uploadFromUrl(imageUrl, {
      folder: uploadFolder,
      quality: "auto",
      format: "auto",
      transformation: [
        { quality: "auto" },
        { fetch_format: "auto" }
      ]
    });

    return NextResponse.json({
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      type: type,
      originalUrl: imageUrl,
    });

  } catch (error) {
    console.error("URL upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload image from URL" },
      { status: 500 }
    );
  }
}

function getUploadFolder(type: string): string {
  switch (type) {
    case "blog-cover":
      return "blog/covers";
    case "blog-featured":
      return "blog/featured";
    case "blog-content":
      return "blog/content";
    default:
      return "blog/misc";
  }
}

// GET method to check Cloudinary status
export async function GET() {
  try {
    const isConfigured = validateCloudinaryConfig();
    
    return NextResponse.json({
      configured: isConfigured,
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || null,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to check Cloudinary status" },
      { status: 500 }
    );
  }
}
