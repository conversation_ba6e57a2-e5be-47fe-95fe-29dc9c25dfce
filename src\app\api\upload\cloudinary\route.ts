import { NextRequest, NextResponse } from "next/server";
import { uploadToCloudinary, uploadFromUrl, validateCloudinaryConfig } from "@/lib/cloudinary";
import { z } from "zod";

// Validation schemas
const FileUploadSchema = z.object({
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

const UrlUploadSchema = z.object({
  imageUrl: z.string().url("Must be a valid URL"),
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

// Define allowed file types
const allowedFileTypes = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/svg+xml",
];

// Define maximum file size (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can upload files
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Validate Cloudinary configuration
    if (!validateCloudinaryConfig()) {
      return NextResponse.json(
        { error: "Cloudinary configuration is missing" },
        { status: 500 }
      );
    }

    const contentType = request.headers.get("content-type");

    // Handle file upload
    if (contentType?.includes("multipart/form-data")) {
      return await handleFileUpload(request);
    }

    // Handle URL upload
    if (contentType?.includes("application/json")) {
      return await handleUrlUpload(request);
    }

    return NextResponse.json(
      { error: "Unsupported content type" },
      { status: 400 }
    );

  } catch (error) {
    console.error("POST /api/upload/cloudinary error:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

async function handleFileUpload(request: NextRequest) {
  try {
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string || "blog-content";
    const folder = formData.get("folder") as string;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate input
    const validation = FileUploadSchema.safeParse({ type, folder });
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      return NextResponse.json(
        { error: `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}` },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size exceeds the limit of 50MB" },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);

    // Upload to Cloudinary with optimizations
    const result = await uploadToCloudinary(buffer, {
      folder: uploadFolder,
      quality: "auto",
      format: "auto",
      transformation: [
        { quality: "auto" },
        { fetch_format: "auto" }
      ]
    });

    return NextResponse.json({
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      type: type,
    });

  } catch (error) {
    console.error("File upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload file to Cloudinary" },
      { status: 500 }
    );
  }
}

async function handleUrlUpload(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validation = UrlUploadSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    const { imageUrl, type, folder } = validation.data;

    // Sanitize and validate URL
    const sanitizedUrl = imageUrl.trim();

    try {
      new URL(sanitizedUrl);
    } catch {
      return NextResponse.json(
        { error: "Invalid URL format. Please provide a valid image URL." },
        { status: 400 }
      );
    }

    // Check if URL is accessible
    try {
      const headResponse = await fetch(sanitizedUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; CloudinaryUploader/1.0)',
        },
      });

      if (!headResponse.ok) {
        return NextResponse.json(
          { error: `Image URL is not accessible: ${headResponse.status} ${headResponse.statusText}` },
          { status: 400 }
        );
      }

      const contentType = headResponse.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        return NextResponse.json(
          { error: "URL does not point to a valid image file." },
          { status: 400 }
        );
      }
    } catch (fetchError) {
      return NextResponse.json(
        { error: "Unable to access the image URL. Please check if the URL is correct and publicly accessible." },
        { status: 400 }
      );
    }

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);

    // Upload from URL to Cloudinary
    const result = await uploadFromUrl(sanitizedUrl, {
      folder: uploadFolder,
      quality: "auto",
      format: "auto",
      transformation: [
        { quality: "auto" },
        { fetch_format: "auto" }
      ]
    });

    return NextResponse.json({
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      type: type,
      originalUrl: sanitizedUrl,
    });

  } catch (error) {
    console.error("URL upload error:", error);

    // Provide more specific error messages
    let errorMessage = "Failed to upload image from URL";

    if (error instanceof Error) {
      if (error.message.includes('Invalid image file')) {
        errorMessage = "The URL does not contain a valid image file.";
      } else if (error.message.includes('File size too large')) {
        errorMessage = "The image file is too large. Please use an image smaller than 50MB.";
      } else if (error.message.includes('Invalid URL')) {
        errorMessage = "The provided URL is invalid or inaccessible.";
      } else if (error.message.includes('Network')) {
        errorMessage = "Network error: Unable to fetch the image from the provided URL.";
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

function getUploadFolder(type: string): string {
  switch (type) {
    case "blog-cover":
      return "blog/covers";
    case "blog-featured":
      return "blog/featured";
    case "blog-content":
      return "blog/content";
    default:
      return "blog/misc";
  }
}

// GET method to check Cloudinary status
export async function GET() {
  try {
    const isConfigured = validateCloudinaryConfig();

    return NextResponse.json({
      configured: isConfigured,
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || null,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to check Cloudinary status" },
      { status: 500 }
    );
  }
}
